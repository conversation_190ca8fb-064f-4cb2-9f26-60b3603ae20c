-------------------------------------------------------------------------------
Test set: com.gl.service.installationPackage.controller.InstallationPackageLogControllerTest
-------------------------------------------------------------------------------
Tests run: 17, Failures: 3, Errors: 0, Skipped: 0, Time elapsed: 23.957 s <<< FAILURE! - in com.gl.service.installationPackage.controller.InstallationPackageLogControllerTest
testDelete_WithoutPermission_ShouldReturnForbidden  Time elapsed: 1.568 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<403> but was:<200>
	at com.gl.service.installationPackage.controller.InstallationPackageLogControllerTest.testDelete_WithoutPermission_ShouldReturnForbidden(InstallationPackageLogControllerTest.java:229)

testRenewal_WithoutPermission_ShouldReturnForbidden  Time elapsed: 1.574 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<403> but was:<200>
	at com.gl.service.installationPackage.controller.InstallationPackageLogControllerTest.testRenewal_WithoutPermission_ShouldReturnForbidden(InstallationPackageLogControllerTest.java:309)

testList_WithoutPermission_ShouldReturnForbidden  Time elapsed: 0.739 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<403> but was:<200>
	at com.gl.service.installationPackage.controller.InstallationPackageLogControllerTest.testList_WithoutPermission_ShouldReturnForbidden(InstallationPackageLogControllerTest.java:167)

